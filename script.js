// --- Constants and Global Variables ---
// Centered roughly on Augusta/Martinez area, or use null for auto-location attempt
const initialCoords = [33.4735, -82.0740];
const map = L.map('map').setView(initialCoords, 13);
const targetList = document.querySelector('#target-list-section #target-list-items');
const sendFeedbackEl = document.getElementById('send-feedback'); // Feedback element
// Grid control buttons - declared when needed to avoid null references
let highlightedCell = null;
let targetMarkers = []; // Holds the L.marker objects
let currentGridLines = [];
let drawnRectangle = null;
const LOCAL_STORAGE_KEY = 'fscsObserverTargets'; // Key for local storage

// Demo mode - works without backend server
const DEMO_MODE = true; // Set to true for GitHub demo
let demoTargets = []; // Simulated backend storage for demo

// Status management variables
let connectionStatus = 'connecting';
let lastSaveTime = null;
let autoSaveInterval = null;

// Map tools variables
// Measurement tools removed for cleaner codebase

// Batch operations variables
let batchSelectionMode = false;
let selectedTargets = new Set();

// --- Helper Functions ---

// --- Initialization ---
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors',
    maxZoom: 19, // Set max zoom level supported by tiles
}).addTo(map);

// --- Status Management Functions ---
function updateConnectionStatus(status) {
    connectionStatus = status;
    const statusElement = document.getElementById('connection-status');
    const textElement = document.getElementById('connection-text');

    if (!statusElement) return;

    // Remove all status classes
    statusElement.classList.remove('connected', 'disconnected', 'connecting');

    switch (status) {
        case 'connected':
            statusElement.classList.add('connected');
            textElement.textContent = 'Connected';
            break;
        case 'disconnected':
            statusElement.classList.add('disconnected');
            textElement.textContent = 'Disconnected';
            break;
        case 'connecting':
        default:
            statusElement.classList.add('connecting');
            textElement.textContent = 'Connecting...';
            break;
    }
}

function updateTargetCount() {
    const count = targetList ? targetList.children.length : 0;
    const textElement = document.getElementById('target-count-text');
    if (textElement) {
        textElement.textContent = `${count} Target${count !== 1 ? 's' : ''}`;
    }

    // Update target summary dashboard
    updateTargetSummary();
}

function updateTargetSummary() {
    if (!targetList) return;

    const targets = Array.from(targetList.children).map(item => item.targetData).filter(Boolean);

    // Count totals
    const totalTargets = targets.length;
    const enemyTargets = targets.filter(t => t.forceType === 'enemy').length;
    const friendlyTargets = targets.filter(t => t.forceType === 'friendly').length;

    // Count high priority targets (tanks, artillery, anti-aircraft)
    const priorityTypes = ['tank', 'artillery', 'antiair'];
    const priorityTargets = targets.filter(t => priorityTypes.includes(t.targetType)).length;

    // Update dashboard elements with animation
    updateSummaryCard('total-targets', totalTargets);
    updateSummaryCard('enemy-targets', enemyTargets);
    updateSummaryCard('friendly-targets', friendlyTargets);
    updateSummaryCard('priority-targets', priorityTargets);
}

function updateSummaryCard(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const currentValue = parseInt(element.textContent) || 0;

    if (currentValue !== newValue) {
        // Add update animation
        const card = element.closest('.summary-card');
        if (card) {
            card.classList.add('updated');
            setTimeout(() => {
                card.classList.remove('updated');
            }, 600);
        }

        // Animate number change
        element.style.animation = 'number-update 0.6s ease-out';
        element.textContent = newValue;

        setTimeout(() => {
            element.style.animation = '';
        }, 600);
    }
}

function updateAutoSaveStatus(status, message = '') {
    const statusElement = document.getElementById('auto-save-status');
    const textElement = document.getElementById('save-text');

    if (!statusElement) return;

    // Remove all status classes
    statusElement.classList.remove('saving', 'saved', 'error');

    switch (status) {
        case 'saving':
            statusElement.classList.add('saving');
            textElement.textContent = 'Saving...';
            break;
        case 'saved':
            statusElement.classList.add('saved');
            textElement.textContent = message || 'Auto-saved';
            lastSaveTime = new Date();
            break;
        case 'error':
            statusElement.classList.add('error');
            textElement.textContent = message || 'Save failed';
            break;
    }
}

function updateGridCoordinates(lat, lng) {
    const textElement = document.getElementById('coordinates-text');
    if (textElement) {
        if (lat !== undefined && lng !== undefined) {
            textElement.textContent = `${lat.toFixed(4)}°, ${lng.toFixed(4)}°`;
        } else {
            textElement.textContent = '--°, --°';
        }
    }
}

function initializeStatusBar() {
    // Initialize connection status
    updateConnectionStatus('connecting');

    // Initialize target count
    updateTargetCount();

    // Initialize auto-save status
    updateAutoSaveStatus('saved', 'Ready');

    // Initialize coordinates
    updateGridCoordinates();

    // Set up mouse move listener for coordinates
    if (map) {
        map.on('mousemove', function(e) {
            updateGridCoordinates(e.latlng.lat, e.latlng.lng);
        });

        map.on('mouseout', function() {
            updateGridCoordinates();
        });
    }

    // Simulate connection after a delay
    setTimeout(() => {
        updateConnectionStatus('connected');
    }, 2000);

    // Set up auto-save interval
    setupAutoSave();
}

function setupAutoSave() {
    // Clear existing interval
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
    }

    // Set up new interval (every 30 seconds)
    autoSaveInterval = setInterval(() => {
        if (targetList && targetList.children.length > 0) {
            updateAutoSaveStatus('saving');
            setTimeout(() => {
                saveTargetsToLocalStorage();
                updateAutoSaveStatus('saved', `Saved ${new Date().toLocaleTimeString()}`);
            }, 500);
        }
    }, 30000);
}

// --- Local Storage Functions ---
function saveTargetsToLocalStorage() {
    const targetsToSave = [];
    targetList.querySelectorAll('li').forEach(item => {
        if (item.targetData) { // Check if targetData exists
            targetsToSave.push(item.targetData);
        }
    });
    try {
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(targetsToSave));
        console.log("Targets saved to localStorage");
        updateAutoSaveStatus('saved', `Saved ${new Date().toLocaleTimeString()}`);
    } catch (e) {
        console.error("Error saving to localStorage:", e);
        updateAutoSaveStatus('error', 'Save failed');
        alert("Could not save targets. Local storage might be full or disabled.");
    }
}

function loadTargetsFromLocalStorage() {
    const savedTargets = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedTargets) {
        try {
            const parsedTargets = JSON.parse(savedTargets);
            // Clear existing list/markers before loading to prevent duplicates
            clearAllTargets(false); // Pass false to skip confirmation and storage clearing
            parsedTargets.forEach(targetData => {
                // Add target back to list and map without saving again immediately
                addSavedTargetToListAndMap(targetData);
            });
            console.log(`Loaded ${parsedTargets.length} targets from localStorage`);
        } catch (e) {
            console.error("Error parsing targets from localStorage:", e);
            localStorage.removeItem(LOCAL_STORAGE_KEY); // Clear invalid data
        }
    }
}

// --- Target Management Functions ---

// Helper to add a target item and marker (used by addTarget and load)
function addTargetToListAndMap(targetData) {
    const { forceType, targetType, quantity, locationText, latlng } = targetData; // Use latlng which should be L.latLng object here
    let markerColor = forceType === 'enemy' ? 'red' : 'blue';
    let markerShapeHTML = '';
    const markerSize = 20; // Increased back to 20px for better visibility

    // Determine marker HTML based on type with distinctive shapes
    // Use solid color based on force type (no white background for enemy)
    const bgColor = markerColor; // Always use the marker color (red or blue)
    const borderStyle = `1px solid ${forceType === 'enemy' ? '#990000' : '#000066'}`; // Darker border for definition

    // Common size for all icons - slightly smaller than container to prevent cutoff
    const iconWidth = markerSize - 2; // Leave 1px margin on each side
    const iconHeight = markerSize - 2; // Leave 1px margin on each side

    switch (targetType) {
        case 'infantry':
            // Circle - centered with proper margin
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%; margin: 1px; box-sizing: border-box;"></div>
            `;
            break;
        case 'tank':
            // Triangle - properly centered and sized
            markerShapeHTML = `
                <div style="position: relative; width: ${markerSize}px; height: ${markerSize}px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: 0; height: 0; border-left: ${iconWidth/2}px solid transparent; border-right: ${iconWidth/2}px solid transparent; border-bottom: ${iconHeight}px solid ${bgColor}; border-top: 0; position: relative;">
                        <div style="position: absolute; top: 0; left: -${iconWidth/2}px; width: 0; height: 0; border-left: ${iconWidth/2}px solid transparent; border-right: ${iconWidth/2}px solid transparent; border-bottom: ${iconHeight}px solid transparent; border-top: 0; outline: ${borderStyle}; outline-offset: -1px;"></div>
                    </div>
                </div>
            `;
            break;
        case 'mg':
            // Square - centered with proper margin
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; margin: 1px; box-sizing: border-box;"></div>
            `;
            break;
        case 'artillery':
            // Diamond - properly centered to prevent cutoff
            markerShapeHTML = `
                <div style="position: relative; width: ${markerSize}px; height: ${markerSize}px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: ${iconWidth * 0.8}px; height: ${iconHeight * 0.8}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle}; box-sizing: border-box;"></div>
                </div>
            `;
            break;
        case 'antiair':
            // Diamond with cross - properly centered
            markerShapeHTML = `
                <div style="position: relative; width: ${markerSize}px; height: ${markerSize}px; display: flex; align-items: center; justify-content: center;">
                    <div style="width: ${iconWidth * 0.8}px; height: ${iconHeight * 0.8}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle}; box-sizing: border-box;"></div>
                    <div style="position: absolute; top: 50%; left: 10%; width: 80%; height: 1px; background-color: white; z-index: 1; transform: translateY(-50%);"></div>
                    <div style="position: absolute; top: 10%; left: 50%; width: 1px; height: 80%; background-color: white; z-index: 1; transform: translateX(-50%);"></div>
                </div>
            `;
            break;
        case 'recon':
            // Hexagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth*0.25},0 ${iconWidth*0.75},0 ${iconWidth},${iconHeight*0.5} ${iconWidth*0.75},${iconHeight} ${iconWidth*0.25},${iconHeight} 0,${iconHeight*0.5}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        case 'apc':
            // Rectangle with rounded corners
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth*1.2}px; height: ${iconHeight*0.8}px; border-radius: 5px;"></div>
            `;
            break;
        case 'heli':
            // Pentagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth},${iconHeight/3} ${iconWidth*0.8},${iconHeight} ${iconWidth*0.2},${iconHeight} 0,${iconHeight/3}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        case 'command':
            // Star
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth*0.65},${iconHeight*0.35} ${iconWidth},${iconHeight*0.35} ${iconWidth*0.75},${iconHeight*0.6} ${iconWidth*0.85},${iconHeight} ${iconWidth/2},${iconHeight*0.75} ${iconWidth*0.15},${iconHeight} ${iconWidth*0.25},${iconHeight*0.6} 0,${iconHeight*0.35} ${iconWidth*0.35},${iconHeight*0.35}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `;
            break;
        default: // Fallback for unknown types
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%;"></div>
            `;
            break;
    }

    const listItem = document.createElement('li');
    listItem.classList.add('new-target'); // Add class for animation
    // Store data on the list item itself for easier access later
    // Ensure we store the plain latlng object for saving, but keep L.latLng for use
    listItem.targetData = {
         ...targetData, // Copy existing data
         latlng: { lat: latlng.lat, lng: latlng.lng } // Store plain object for saving
    };

    // Determine if target is high priority
    const priorityTypes = ['tank', 'artillery', 'antiair'];
    const isHighPriority = priorityTypes.includes(targetType);

    listItem.innerHTML = `
        <div class="target-item-header">
            <div class="target-type-badge ${forceType}">
                ${targetType.toUpperCase()}
                ${isHighPriority ? '<span class="target-priority-indicator high">!</span>' : ''}
            </div>
            <div class="target-force-indicator">${forceType === 'enemy' ? '[HOSTILE]' : '[FRIENDLY]'} ${forceType.toUpperCase()}</div>
        </div>
        <div class="target-details">
            <div class="target-detail-item">
                <span class="target-detail-icon">QTY:</span>
                <span>${quantity}</span>
            </div>
            <div class="target-detail-item target-location">
                <span class="target-detail-icon">LOC:</span>
                <span class="location-text" title="${locationText}">${locationText}</span>
            </div>
        </div>
        <div class="button-group">
            <button class="go-to-button" title="Zoom to target">LOCATE</button>
            <button class="remove-button" title="Remove target">REMOVE</button>
        </div>
    `;
    targetList.appendChild(listItem);

    // Create the marker using the L.latLng object passed in targetData
    const marker = L.marker(latlng, {
        title: `${forceType} ${targetType} (${quantity})`, // Add title for hover tooltip
        icon: L.divIcon({
            className: `target-marker target-${forceType} target-${targetType}`,
            iconSize: [markerSize, markerSize],
            // Adjust anchor based on shape (bottom-center for tank, center-center for others)
            iconAnchor: targetType === 'tank' ? [markerSize / 2, markerSize] : [markerSize / 2, markerSize / 2],
            html: markerShapeHTML
        })
    }).addTo(map);
    targetMarkers.push(marker); // Add marker to the global array

    listItem.marker = marker; // Link marker to list item

    // Add event listeners for buttons
    listItem.querySelector('.go-to-button').addEventListener('click', () => {
        console.log('Go To clicked for:', latlng); // Use the L.latLng object directly
        if (latlng) {
            map.setView(latlng, 19); // Zoom level set to 19
        }
    });

    listItem.querySelector('.remove-button').addEventListener('click', () => {
        map.removeLayer(listItem.marker);
        const markerIndex = targetMarkers.indexOf(listItem.marker);
        if (markerIndex > -1) {
            targetMarkers.splice(markerIndex, 1);
        }
        listItem.remove();

        // Update status indicators
        updateAutoSaveStatus('saving');
        setTimeout(() => {
            saveTargetsToLocalStorage(); // Save after removing
            updateTargetCount(); // Update target count in status bar
        }, 300);
    });

     // Remove the 'new-target' class after a short delay to allow the animation to play
     setTimeout(() => {
        listItem.classList.remove('new-target');
    }, 1000);
}

// Function called only when loading from storage
function addSavedTargetToListAndMap(targetData) {
    // Directly use the saved data, converting plain lat/lng object back to L.latLng
    if (targetData && targetData.latlng && typeof targetData.latlng.lat === 'number' && typeof targetData.latlng.lng === 'number') {
         const leafletLatLng = L.latLng(targetData.latlng.lat, targetData.latlng.lng);
         // Pass the full data including the L.latLng object to the main add function
         addTargetToListAndMap({...targetData, latlng: leafletLatLng });
    } else {
        console.warn("Skipping saved target with missing/invalid latlng data:", targetData);
    }
}


// Function called when the "Add Target" button is clicked
function addTarget() {
    const addButton = document.getElementById('add-target-button');

    // Add visual feedback
    addButtonRippleEffect(addButton);

    const forceType = document.getElementById('force-type').value;
    const targetType = document.getElementById('target-type').value;
    const quantityInput = document.getElementById('target-quantity');
    const quantity = parseInt(quantityInput.value, 10);
    const locationInput = document.getElementById('target-location');
    const locationValue = locationInput.value.trim(); // Use trimmed value

    // --- Enhanced Input Validation ---
    if (!validateAllFields()) {
        // Show a user-friendly message
        const firstErrorField = document.querySelector('.form-group.has-error input, .form-group.has-error select');
        if (firstErrorField) {
            firstErrorField.focus();

            // Add a subtle shake animation to draw attention
            firstErrorField.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                firstErrorField.style.animation = '';
            }, 500);
        }
        return;
    }

    const locationParts = locationValue.split(' | ');
    let displayLocationText = locationValue; // Text to show in the list
    let processingLocationText = locationValue; // Text to parse coords from
    let gridInfo = "";
    let targetLatLng = null;
    let rawMgrsString = ""; // To store MGRS string for validation

    // --- Location Parsing Logic ---
    // Separate grid info from the rest for parsing coordinates
    if (locationParts.length > 1 && locationParts[0].startsWith('Grid:')) {
        gridInfo = ` (${locationParts[0]})`;
        // Use only the coordinate part for parsing lat/lon or MGRS
        processingLocationText = locationParts.slice(1).join(' | ');
        // Keep the full string for display unless MGRS is validated later
        displayLocationText = locationValue;
    } else {
        processingLocationText = locationValue; // Use the whole input if no grid info
        displayLocationText = locationValue;
    }

    // Try to extract Lat/Lon first from the processing text
    const latLngMatch = processingLocationText.match(/Lat: (-?\d+(\.\d+)?), Lon: (-?\d+(\.\d+)?)/); // Allow integer coords too
    if (latLngMatch) {
        targetLatLng = L.latLng(parseFloat(latLngMatch[1]), parseFloat(latLngMatch[3])); // Use correct capture groups
        // If Lat/Lon found, make it the primary display text besides grid info
        displayLocationText = `Lat: ${targetLatLng.lat.toFixed(6)}, Lon: ${targetLatLng.lng.toFixed(6)}`;
    }

    // Try to extract MGRS from the processing text
    if (processingLocationText.includes('MGRS:')) {
        rawMgrsString = processingLocationText.split('MGRS: ')[1].trim();
        if (!rawMgrsString) {
             // Handle empty MGRS string if it occurs
             console.warn("Empty MGRS string detected in input.");
             // Decide how to handle: maybe ignore MGRS part or require user input
        } else if (typeof mgrs !== 'undefined') {
            try {
                // *** MGRS Validation Step ***
                const convertedLatLng = mgrs.toLatLng(rawMgrsString); // Attempt conversion
                // If conversion succeeds and we didn't already get Lat/Lon, use it
                if (!targetLatLng) {
                    targetLatLng = L.latLng(convertedLatLng[0], convertedLatLng[1]);
                }
                 // Prioritize showing MGRS in the list if it's valid
                 displayLocationText = `MGRS: ${rawMgrsString}`;
            } catch (e) {
                console.error("MGRS conversion error:", e);
                // *** MGRS Validation Feedback ***
                alert(`Invalid MGRS format: "${rawMgrsString}". Please ensure it's correct or use Lat/Lon.`);
                return; // Stop processing if MGRS is explicitly provided but invalid
            }
        } else {
            // Should not happen if library is loaded, but good practice
            alert("MGRS library not loaded, cannot validate or use MGRS coordinates.");
            return;
        }
    }

    // If after all checks, we still don't have LatLng (e.g., bad input format)
    if (!targetLatLng) {
        alert("Could not determine target coordinates from input: " + locationValue + "\nPlease click the map to set a location.");
        return;
    }

    // --- Prepare Data and Add ---
    const targetData = {
        forceType,
        targetType,
        quantity,
        locationText: displayLocationText, // Use the cleaned display text
        gridInfo,
        latlng: targetLatLng // Pass the L.latLng object to the add helper
    };

    // Use the helper to add to list and map
    addTargetToListAndMap(targetData);

    // Add success feedback
    showButtonSuccess(addButton);

    // Announce to screen readers
    announceToScreenReader(`Target added: ${targetType} at ${locationText}`);

    // --- Post-Add Actions ---
    updateAutoSaveStatus('saving');
    setTimeout(() => {
        saveTargetsToLocalStorage(); // Save after adding a new target
        updateTargetCount(); // Update target count in status bar
    }, 300);

    locationInput.value = ""; // Clear input field
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }
}

// Function to clear all targets
function clearAllTargets(confirmBeforeClear = true) { // Add optional flag
    let doClear = false;
    if (confirmBeforeClear) {
        doClear = confirm("Are you sure you want to remove ALL targets from the list, map, and saved data?");
    } else {
        doClear = true; // Skip confirmation if flag is false
    }

    if (doClear) {
        // Remove markers from map and clear array
        targetMarkers.forEach(marker => map.removeLayer(marker));
        targetMarkers = [];

        // Clear the list in the UI
        targetList.innerHTML = '';

        // Clear local storage only if confirmation was sought or implied
        if (confirmBeforeClear) {
             localStorage.removeItem(LOCAL_STORAGE_KEY);
             console.log("All targets cleared from UI, Map, and localStorage.");
        } else {
             console.log("All targets cleared from UI and Map (localStorage untouched).");
        }

        // Update status indicators
        updateTargetCount(); // Update target count in status bar
        updateAutoSaveStatus('saved', 'Cleared');
    }
}

// Function to send data (with feedback using Fetch API)
async function sendTargetData() { // Changed to async function for await
    const targetListData = [];
    const targetListItems = targetList.querySelectorAll('li');

    targetListItems.forEach(item => {
        if (item.targetData) { // Use stored data from the list item
             targetListData.push(item.targetData); // targetData already has plain lat/lng
        }
    });

    if (targetListData.length === 0) {
        // Show validation error instead of alert
        const sendButton = document.getElementById('send-targets-button');
        setFieldValidationState(sendButton, 'error', 'No targets to send');
        setTimeout(() => {
            setFieldValidationState(sendButton, 'clear');
        }, 3000);
        return Promise.reject('No targets to send');
    }

    // Demo mode - simulate backend without server
    if (DEMO_MODE) {
        return sendTargetDataDemo(targetListData);
    }

    // --- Enhanced Visual Feedback ---
    const sendButton = document.getElementById('send-targets-button');
    const originalText = sendButton.textContent;

    // Update connection status to show sending
    updateConnectionStatus('connecting');

    // Show loading state on button with enhanced feedback
    const loadingState = showButtonLoading(sendButton, 'Transmitting...');
    sendButton.classList.add('sending');

    // Show feedback message
    if (sendFeedbackEl) {
        sendFeedbackEl.textContent = "Transmitting target data...";
        sendFeedbackEl.className = 'feedback-message sending';
    }

    // --- Define the API endpoint ---
    // !!! This now points to your local backend server !!!
    const apiUrl = 'http://localhost:3000/api/sendTargets'; // Use full URL

    try {
        // --- Use Fetch API to send data ---
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add any other headers like authorization tokens if needed
            },
            body: JSON.stringify(targetListData) // Convert target data array to JSON string
        });

        // Check if the server responded successfully (status code 2xx)
        if (response.ok) {
            // Optional: Process response data from server if needed
            // const responseData = await response.json();
            // console.log('Server response:', responseData);

            // Update connection status to connected
            updateConnectionStatus('connected');

            // Show success feedback
            if (sendFeedbackEl) {
                sendFeedbackEl.textContent = "[SUCCESS] Targets transmitted successfully";
                sendFeedbackEl.classList.remove('sending');
                sendFeedbackEl.classList.add('success');
            }

            // Show success state on button with enhanced feedback
            loadingState.stop();
            showButtonSuccess(sendButton);

            console.log('Target data sent successfully.');
            // Optionally clear targets after successful send?
            // clearAllTargets(false); // Example: Clear UI/Map but not storage

        } else {
            // Server responded with an error status code (4xx or 5xx)
            updateConnectionStatus('disconnected');

            let errorText = `[ERROR] Server error: ${response.status} ${response.statusText}`;
             try {
                 const serverError = await response.text(); // Try to get more details
                 if (serverError) {
                      errorText += `. ${serverError.substring(0, 100)}`; // Limit length
                 }
             } catch (e) {
                  console.warn("Could not read error response body.");
             }

            if (sendFeedbackEl) {
                sendFeedbackEl.textContent = errorText;
                sendFeedbackEl.classList.remove('sending');
                sendFeedbackEl.classList.add('error');
            }

            loadingState.stop();
            showButtonError(sendButton, 'Send Failed');

            console.error(`Error sending target data: ${response.status} ${response.statusText}`);
        }

    } catch (error) {
        // Handle network errors or other issues with the fetch call itself
        updateConnectionStatus('disconnected');

        if (sendFeedbackEl) {
            sendFeedbackEl.textContent = "[ERROR] Network error. Check server connection.";
            sendFeedbackEl.classList.remove('sending');
            sendFeedbackEl.classList.add('error');
        }

        loadingState.stop();
        showButtonError(sendButton, 'Connection Error');

        console.error('Network error or other issue sending target data:', error);
    } finally {
        // Re-enable the button and reset after a delay
        setTimeout(() => {
            sendButton.disabled = false;
            sendButton.classList.remove('sending', 'success', 'error');
            sendButton.innerHTML = originalText;

            // Clear feedback message
            if (sendFeedbackEl) {
                sendFeedbackEl.textContent = "";
                sendFeedbackEl.className = 'feedback-message';
            }
        }, 3000); // Keep message for 3 seconds
    }
}


// --- Grid Drawing and Map Interaction ---

// Function to set up collapsible legend
function setupCollapsibleLegend() {
    const mapLegend = document.getElementById('map-legend');
    if (!mapLegend) return;

    const legendHeader = mapLegend.querySelector('h4');
    if (!legendHeader) return;

    // Set initial state (expanded by default)
    mapLegend.classList.add('expanded');

    // Toggle legend on header click
    legendHeader.addEventListener('click', () => {
        if (mapLegend.classList.contains('expanded')) {
            mapLegend.classList.remove('expanded');
            mapLegend.classList.add('collapsed');
        } else {
            mapLegend.classList.remove('collapsed');
            mapLegend.classList.add('expanded');
        }
    });
}

function clearGrid() {
    currentGridLines.forEach(line => map.removeLayer(line));
    currentGridLines = [];
    if (drawnRectangle) {
        map.removeLayer(drawnRectangle);
        drawnRectangle = null;
    }
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }
}

function drawGrid(bounds, gridSize) {
    clearGrid();
    const southWest = bounds.getSouthWest();
    const northEast = bounds.getNorthEast();
    // Use Leaflet's distanceTo for potentially better accuracy across latitudes
    const gridWidthMeters = southWest.distanceTo(L.latLng(southWest.lat, northEast.lng));
    const gridHeightMeters = southWest.distanceTo(L.latLng(northEast.lat, southWest.lng));
    // Ensure at least one row/column
    const numCols = Math.max(1, Math.ceil(gridWidthMeters / gridSize));
    const numRows = Math.max(1, Math.ceil(gridHeightMeters / gridSize));
    const latStep = (northEast.lat - southWest.lat) / numRows;
    const lngStep = (northEast.lng - southWest.lng) / numCols;
    const newGridLines = [];
    const gridLineOptions = { color: '#555', weight: 1, interactive: false }; // Darker grid lines
    // Draw Vertical Lines
    for (let i = 0; i <= numCols; i++) {
        const lng = southWest.lng + i * lngStep;
        newGridLines.push(L.polyline([L.latLng(southWest.lat, lng), L.latLng(northEast.lat, lng)], gridLineOptions).addTo(map));
    }
    // Draw Horizontal Lines
    for (let j = 0; j <= numRows; j++) {
        const lat = southWest.lat + j * latStep;
        newGridLines.push(L.polyline([L.latLng(lat, southWest.lng), L.latLng(lat, northEast.lng)], gridLineOptions).addTo(map));
    }
    currentGridLines = newGridLines;
    // Make drawn rectangle non-interactive with clearer styling
    drawnRectangle = L.rectangle(bounds, { color: '#3388ff', fillOpacity: 0.1, weight: 2, interactive: false }).addTo(map);
}

// Setup Leaflet.Draw control
// Enable the rectangle tool in the map toolbar to ensure it works.
const drawControl = new L.Control.Draw({
    draw: {
        polyline: false,
        polygon: false,
        circle: false,
        marker: false, // Disable standard marker draw tool
        circlemarker: false,
        rectangle: { // Enable the default rectangle tool button
             shapeOptions: { color: '#3388ff', fillOpacity: 0.1, weight: 2 },
             tooltip: { start: 'Click and drag to draw grid area.' }
        }
    },
    edit: {
        featureGroup: new L.FeatureGroup() // Required even if editing is disabled
    }
});
map.addControl(drawControl);

// Handle creation of the grid rectangle
map.on('draw:created', function (e) {
    if (e.layerType === 'rectangle') {
        const bounds = e.layer.getBounds();
        const gridSizeInput = document.getElementById('grid-size');
        if (!gridSizeInput) {
            console.error("Grid size input element not found.");
            return;
        }
        const gridSize = parseFloat(gridSizeInput.value);
        if (!isNaN(gridSize) && gridSize > 0) {
            drawGrid(bounds, gridSize);
        } else {
            alert("Please enter a valid positive grid size (e.g., 100).");
        }
        // We won't disable drawing automatically here if using the default button,
        // as the user might want to draw multiple rectangles.
    }
});

// --- Retro Mode Functions ---
function toggleRetroMode() {
    const body = document.body;

    // Toggle retro mode class
    body.classList.toggle('retro-mode');

    // Update button text
    const themeToggle = document.getElementById('retro-mode-toggle');
    if (themeToggle) {
        themeToggle.textContent = body.classList.contains('retro-mode')
            ? 'Standard Mode'
            : 'Retro Mode';
    }

    // Save preference to localStorage
    localStorage.setItem('vectorFireTheme', body.classList.contains('retro-mode') ? 'retro' : 'standard');

    // Play a retro toggle sound
    playToggleSound();
}

function playToggleSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(document.body.classList.contains('retro-mode') ? 440 : 880, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (e) {
        console.log('Audio not supported or enabled');
    }
}

function initializeTheme() {
    // Load saved theme preference
    const savedTheme = localStorage.getItem('vectorFireTheme');
    if (savedTheme === 'retro') {
        document.body.classList.add('retro-mode');
        const themeToggle = document.getElementById('retro-mode-toggle');
        if (themeToggle) {
            themeToggle.textContent = 'Standard Mode';
        }
    }
}

function initializeCollapsibles() {
    // Initialize collapsible sections
    const collapsibles = document.querySelectorAll(".collapsible");

    collapsibles.forEach(function(collapsible) {
        collapsible.addEventListener("click", function() {
            this.classList.toggle("active");
            const content = this.nextElementSibling;

            if (content.style.maxHeight) {
                // Collapsing
                content.style.maxHeight = null;
                content.style.visibility = "hidden";
                content.style.opacity = "0";
                content.style.padding = "0";
            } else {
                // Expanding
                content.style.visibility = "visible";
                content.style.opacity = "1";
                content.style.padding = "calc(var(--spacing-unit) * 3.5)";
                content.style.maxHeight = content.scrollHeight + "px";
            }
        });
    });
}

// Add event listener to the custom Draw Grid Area button
// Using DOMContentLoaded to ensure the button element exists
document.addEventListener('DOMContentLoaded', () => {
    // Set up the rectangle icon in the instructions to highlight the map tool
    const rectangleIcon = document.querySelector('.rectangle-icon');
    const rectangleButton = document.querySelector('.leaflet-draw-draw-rectangle');

    if (rectangleIcon && rectangleButton) {
        // Highlight the map tool when hovering over the icon in instructions
        rectangleIcon.addEventListener('mouseenter', () => {
            rectangleButton.classList.add('highlight-tool');
        });

        rectangleIcon.addEventListener('mouseleave', () => {
            rectangleButton.classList.remove('highlight-tool');
        });

        // Make the icon clickable to activate the draw tool
        rectangleIcon.style.cursor = 'pointer';
        rectangleIcon.addEventListener('click', () => {
            if (drawControl.handlers.rectangle) {
                drawControl.handlers.rectangle.enable();
                rectangleButton.classList.add('highlight-tool');

                // Remove highlight when drawing starts or is canceled
                const removeHighlight = () => {
                    rectangleButton.classList.remove('highlight-tool');
                };

                map.once('draw:drawstart', removeHighlight);
                map.once('draw:drawstop', removeHighlight);
                map.once('draw:canceled', removeHighlight);
            }
        });
    }

    // Add event listener for the Clear Grid Lines button
    const clearGridLinesButton = document.getElementById('clear-grid-lines-button');
    if (clearGridLinesButton) {
        clearGridLinesButton.addEventListener('click', clearGrid);
    } else {
         console.error("Clear Grid Lines Button element not found!");
    }

    // Set up collapsible legend
    setupCollapsibleLegend();

    // Load saved targets when the DOM is ready
    loadTargetsFromLocalStorage();
});


// Handle map clicks for location input and grid highlighting
map.on('click', (e) => {
    const clickedLatLng = e.latlng;
    const locationInput = document.getElementById('target-location');
    locationInput.value = ""; // Clear previous input first

    // Clear previous highlight
    if (highlightedCell) {
        map.removeLayer(highlightedCell);
        highlightedCell = null;
    }

    let locationString = "";
    let gridRef = "";
    let mgrsString = "";

    // --- Generate MGRS/LatLon String ---
    if (typeof mgrs !== 'undefined' && mgrs.toMGRS) {
        try {
            // Get MGRS with 5-digit precision (1m)
            mgrsString = mgrs.toMGRS(clickedLatLng, 5);
            locationString = `MGRS: ${mgrsString}`;
        } catch (err) {
            console.error("Error converting to MGRS on click:", err);
            // Fallback to Lat/Lon if MGRS fails
            locationString = `Lat: ${clickedLatLng.lat.toFixed(6)}, Lon: ${clickedLatLng.lng.toFixed(6)}`;
        }
    } else {
        // Fallback to Lat/Lon if mgrs library not loaded
        locationString = `Lat: ${clickedLatLng.lat.toFixed(6)}, Lon: ${clickedLatLng.lng.toFixed(6)}`;
    }

    // --- Check if inside drawn grid and highlight cell ---
    if (drawnRectangle && currentGridLines.length > 0 && drawnRectangle.getBounds().contains(clickedLatLng)) {
        const bounds = drawnRectangle.getBounds();
        const southWest = bounds.getSouthWest();
        const northEast = bounds.getNorthEast();
        const gridSizeInput = document.getElementById('grid-size'); // Check grid size input exists
        const gridSize = gridSizeInput ? parseFloat(gridSizeInput.value) : NaN;

        if (!isNaN(gridSize) && gridSize > 0) {
            // Recalculate grid dimensions for accuracy
            const gridWidthMeters = southWest.distanceTo(L.latLng(southWest.lat, northEast.lng));
            const gridHeightMeters = southWest.distanceTo(L.latLng(northEast.lat, southWest.lng));
            // Ensure at least one row/column
            const numCols = Math.max(1, Math.ceil(gridWidthMeters / gridSize));
            const numRows = Math.max(1, Math.ceil(gridHeightMeters / gridSize));
            const latStep = (northEast.lat - southWest.lat) / numRows;
            const lngStep = (northEast.lng - southWest.lng) / numCols;

            // Calculate which cell was clicked (ensure within bounds 0 to N-1)
            const row = Math.min(numRows - 1, Math.max(0, Math.floor((clickedLatLng.lat - southWest.lat) / latStep)));
            const col = Math.min(numCols - 1, Math.max(0, Math.floor((clickedLatLng.lng - southWest.lng) / lngStep)));

            // Calculate bounds of the clicked cell
            const cellSouthWest = L.latLng(southWest.lat + row * latStep, southWest.lng + col * lngStep);
            const cellNorthEast = L.latLng(southWest.lat + (row + 1) * latStep, southWest.lng + (col + 1) * lngStep);

            // Highlight the cell
            highlightedCell = L.rectangle([cellSouthWest, cellNorthEast], { color: 'lime', weight: 2, fillOpacity: 0.3, interactive: false }).addTo(map);

            // Create grid reference string (R1, C1 format)
            gridRef = `Grid: R${row + 1}, C${col + 1}`;
            // Prepend Grid Ref to the location string
            locationString = `${gridRef} | ${locationString}`;
        } else {
             console.warn("Grid present but grid size is invalid. Cannot calculate cell reference.");
             // Keep locationString as just MGRS/LatLon if grid size is bad
        }
    }

    // Update the input field with the final string
    locationInput.value = locationString;
});

// --- Form Validation Functions ---
function createValidationMessage(element, message, type = 'error') {
    // Remove existing validation message
    const existingMessage = element.parentNode.querySelector('.validation-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create new validation message
    const messageElement = document.createElement('div');
    messageElement.className = `validation-message ${type}`;
    messageElement.textContent = message;

    // Add to form group
    element.parentNode.appendChild(messageElement);

    // Show message with animation
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);

    return messageElement;
}

function createValidationIcon(element, type = 'error') {
    // Remove existing validation icon
    const existingIcon = element.parentNode.querySelector('.validation-icon');
    if (existingIcon) {
        existingIcon.remove();
    }

    // Create new validation icon
    const iconElement = document.createElement('div');
    iconElement.className = 'validation-icon';

    switch (type) {
        case 'error':
            iconElement.textContent = '[!]';
            break;
        case 'success':
            iconElement.textContent = '[✓]';
            break;
        case 'loading':
            iconElement.textContent = '[...]';
            break;
        default:
            iconElement.textContent = '[?]';
    }

    // Add to form group
    element.parentNode.appendChild(iconElement);

    return iconElement;
}

function setFieldValidationState(element, state, message = '') {
    const formGroup = element.closest('.form-group');
    if (!formGroup) return;

    // Remove all validation classes
    formGroup.classList.remove('has-error', 'has-success', 'is-loading');

    // Remove existing validation elements
    const existingMessage = formGroup.querySelector('.validation-message');
    const existingIcon = formGroup.querySelector('.validation-icon');

    if (existingMessage) existingMessage.remove();
    if (existingIcon) existingIcon.remove();

    // Add new state
    switch (state) {
        case 'error':
            formGroup.classList.add('has-error');
            if (message) createValidationMessage(element, message, 'error');
            createValidationIcon(element, 'error');
            break;
        case 'success':
            formGroup.classList.add('has-success');
            if (message) createValidationMessage(element, message, 'success');
            createValidationIcon(element, 'success');
            break;
        case 'loading':
            formGroup.classList.add('is-loading');
            if (message) createValidationMessage(element, message, 'info');
            createValidationIcon(element, 'loading');
            break;
        case 'clear':
        default:
            // Just remove all states
            break;
    }
}

function validateQuantityField(quantityInput) {
    const value = quantityInput.value.trim();
    const quantity = parseInt(value, 10);

    if (!value) {
        setFieldValidationState(quantityInput, 'error', 'Quantity is required');
        return false;
    }

    if (isNaN(quantity) || quantity <= 0) {
        setFieldValidationState(quantityInput, 'error', 'Quantity must be a positive number');
        return false;
    }

    if (quantity > 1000) {
        setFieldValidationState(quantityInput, 'error', 'Quantity seems unusually high');
        return false;
    }

    setFieldValidationState(quantityInput, 'success', 'Valid quantity');
    return true;
}

function validateLocationField(locationInput) {
    const value = locationInput.value.trim();

    if (!value) {
        setFieldValidationState(locationInput, 'error', 'Please click on the map to set location');
        return false;
    }

    // Check if it looks like valid coordinates
    const hasLatLon = /Lat:\s*-?\d+(\.\d+)?,\s*Lon:\s*-?\d+(\.\d+)?/.test(value);
    const hasMGRS = /MGRS:\s*\w+/.test(value);

    if (!hasLatLon && !hasMGRS) {
        setFieldValidationState(locationInput, 'error', 'Invalid location format');
        return false;
    }

    setFieldValidationState(locationInput, 'success', 'Valid location');
    return true;
}

function validateDropdownField(selectElement, fieldName) {
    const value = selectElement.value;

    if (!value || value === '') {
        setFieldValidationState(selectElement, 'error', `Please select ${fieldName}`);
        return false;
    }

    setFieldValidationState(selectElement, 'success', `${fieldName} selected`);
    return true;
}

function setupFormValidation() {
    const quantityInput = document.getElementById('target-quantity');
    const locationInput = document.getElementById('target-location');
    const forceTypeSelect = document.getElementById('force-type');
    const targetTypeSelect = document.getElementById('target-type');

    if (quantityInput) {
        quantityInput.addEventListener('input', () => {
            validateQuantityField(quantityInput);
        });

        quantityInput.addEventListener('blur', () => {
            validateQuantityField(quantityInput);
        });
    }

    if (locationInput) {
        locationInput.addEventListener('input', () => {
            validateLocationField(locationInput);
        });
    }

    if (forceTypeSelect) {
        forceTypeSelect.addEventListener('change', () => {
            validateDropdownField(forceTypeSelect, 'force type');
        });
    }

    if (targetTypeSelect) {
        targetTypeSelect.addEventListener('change', () => {
            validateDropdownField(targetTypeSelect, 'target type');
        });
    }
}

function validateAllFields() {
    const quantityInput = document.getElementById('target-quantity');
    const locationInput = document.getElementById('target-location');
    const forceTypeSelect = document.getElementById('force-type');
    const targetTypeSelect = document.getElementById('target-type');

    let isValid = true;

    if (quantityInput && !validateQuantityField(quantityInput)) {
        isValid = false;
    }

    if (locationInput && !validateLocationField(locationInput)) {
        isValid = false;
    }

    if (forceTypeSelect && !validateDropdownField(forceTypeSelect, 'force type')) {
        isValid = false;
    }

    if (targetTypeSelect && !validateDropdownField(targetTypeSelect, 'target type')) {
        isValid = false;
    }

    return isValid;
}

// --- Tooltip System Functions ---
function createTooltip(element, text, type = 'default', position = 'top') {
    // Remove existing tooltip if any
    const existingTooltip = element.querySelector('.tooltip');
    if (existingTooltip) {
        existingTooltip.remove();
    }

    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = `tooltip tooltip-${type} tooltip-${position}`;
    tooltip.textContent = text;

    // Add tooltip to element
    element.appendChild(tooltip);

    // Make element a tooltip container
    element.classList.add('tooltip-container');

    return tooltip;
}

function addHelpIcon(element, tooltipText, type = 'default') {
    // Create help icon
    const helpIcon = document.createElement('span');
    helpIcon.className = 'help-icon tooltip-container';
    helpIcon.textContent = '?';
    helpIcon.setAttribute('tabindex', '0');

    // Create tooltip for help icon
    createTooltip(helpIcon, tooltipText, type);

    // Add help icon after the element
    element.parentNode.insertBefore(helpIcon, element.nextSibling);

    return helpIcon;
}

function wrapWithTooltip(element, tooltipText, className = 'military-term', type = 'military') {
    // Create wrapper span
    const wrapper = document.createElement('span');
    wrapper.className = `${className} tooltip-container`;

    // Move element content to wrapper
    wrapper.innerHTML = element.innerHTML;
    element.innerHTML = '';
    element.appendChild(wrapper);

    // Add tooltip to wrapper
    createTooltip(wrapper, tooltipText, type);

    return wrapper;
}

function setupTooltips() {
    // Military terminology tooltips
    const militaryTerms = {
        'MGRS': 'Military Grid Reference System - A geocoordinate standard used by NATO militaries for locating points on Earth',
        'Grid Reference': 'A system of coordinates used to identify locations on a map using a grid overlay',
        'Bearing': 'The horizontal angle between a reference direction (usually north) and the target direction',
        'Elevation': 'The vertical angle of the mortar barrel above the horizontal plane',
        'Deflection': 'The horizontal adjustment in mils from the base direction to the target',
        'Mils': 'Angular measurement unit where 6400 mils = 360 degrees. Used for precise artillery calculations',
        'Fire Mission': 'A specific target engagement task assigned to artillery or mortar units',
        'Grid Square': 'A square area on a military map defined by grid lines, typically 1000m x 1000m',
        'Target': 'An enemy position, unit, or installation designated for engagement',
        'Observer': 'Personnel responsible for locating and identifying targets for indirect fire',
        'Mortar Team': 'Crew operating mortar weapons for indirect fire support',
        'Force Type': 'Classification of military units as friendly, enemy, or neutral forces'
    };

    // Technical terms tooltips
    const technicalTerms = {
        'Latitude': 'Geographic coordinate specifying north-south position (degrees from equator)',
        'Longitude': 'Geographic coordinate specifying east-west position (degrees from prime meridian)',
        'Coordinates': 'Numerical values that determine a precise location on Earth',
        'Haversine': 'Mathematical formula for calculating distances between two points on a sphere',
        'Ballistic': 'Related to the flight path of projectiles under the influence of gravity',
        'Range Table': 'Reference data showing firing solutions for different distances and conditions',
        'Trajectory': 'The curved path followed by a projectile in flight'
    };

    // Add tooltips to labels and important elements
    const labels = document.querySelectorAll('label');
    labels.forEach(label => {
        const text = label.textContent.trim();

        if (text.includes('Force Type')) {
            addHelpIcon(label, militaryTerms['Force Type'], 'military');
        } else if (text.includes('Target Type') || text.includes('Type')) {
            addHelpIcon(label, militaryTerms['Target'], 'military');
        } else if (text.includes('Quantity')) {
            addHelpIcon(label, 'Number of enemy units or targets observed at this location', 'technical');
        } else if (text.includes('Location')) {
            addHelpIcon(label, 'Geographic position of the target using coordinates or grid reference', 'technical');
        } else if (text.includes('Grid Size')) {
            addHelpIcon(label, militaryTerms['Grid Square'], 'military');
        } else if (text.includes('Mortar Type')) {
            addHelpIcon(label, 'Type and caliber of mortar weapon system being used', 'military');
        } else if (text.includes('Ammunition')) {
            addHelpIcon(label, 'Type of mortar round: HE (High Explosive), Smoke, Illumination, etc.', 'military');
        }
    });

    // Add tooltips to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        const id = button.id;
        const text = button.textContent.trim();

        if (id === 'add-target-button') {
            button.title = 'Add the current target information to the target list for transmission';
        } else if (id === 'send-targets-button') {
            button.title = 'Transmit all targets in the list to the mortar team for fire missions';
        } else if (id === 'clear-targets-button') {
            button.title = 'Remove all targets from the list and map (cannot be undone)';
        } else if (id === 'clear-grid-lines-button') {
            button.title = 'Remove the grid overlay from the map';
        } else if (id === 'calculate-solution-button') {
            button.title = 'Calculate firing data (bearing, elevation, deflection) for the selected target';
        } else if (id === 'refresh-targets-button') {
            button.title = 'Check for new targets received from observer teams';
        }
    });

    // Add tooltips to status indicators
    const statusIndicators = document.querySelectorAll('.status-indicator');
    statusIndicators.forEach(indicator => {
        const id = indicator.id;

        if (id === 'connection-status') {
            indicator.title = 'Communication status with the backend server';
        } else if (id === 'target-count-status') {
            indicator.title = 'Number of targets currently in the target list';
        } else if (id === 'auto-save-status') {
            indicator.title = 'Automatic saving status of target data to local storage';
        } else if (id === 'grid-coordinates') {
            indicator.title = 'Current mouse position coordinates on the map';
        } else if (id === 'received-targets-status') {
            indicator.title = 'Number of targets received from observer teams';
        } else if (id === 'mortar-ready-status') {
            indicator.title = 'Mortar setup status - position, weapon, and ammunition selection';
        }
    });

    // Add tooltips to map legend items
    const legendItems = document.querySelectorAll('.legend-item');
    legendItems.forEach(item => {
        const text = item.textContent.trim();
        if (text.includes('Tank')) {
            item.title = 'Armored fighting vehicle - high priority target';
        } else if (text.includes('Infantry')) {
            item.title = 'Foot soldiers - standard priority target';
        } else if (text.includes('MG')) {
            item.title = 'Machine Gun position - suppression target';
        } else if (text.includes('Artillery')) {
            item.title = 'Artillery position - high priority counter-battery target';
        } else if (text.includes('Anti-Aircraft')) {
            item.title = 'Air defense system - priority target for air operations';
        }
    });
}

// --- Confirmation Dialog System ---
function createConfirmationDialog(options) {
    const {
        title = 'Confirm Action',
        message = 'Are you sure you want to proceed?',
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        type = 'warning',
        preview = null,
        onConfirm = () => {},
        onCancel = () => {}
    } = options;

    // Remove existing dialog if any
    const existingDialog = document.querySelector('.confirmation-overlay');
    if (existingDialog) {
        existingDialog.remove();
    }

    // Create dialog HTML
    const dialogHTML = `
        <div class="confirmation-overlay">
            <div class="confirmation-dialog">
                <div class="confirmation-header">
                    <h3>
                        <span class="warning-icon">⚠️</span>
                        ${title}
                    </h3>
                </div>
                <div class="confirmation-body">
                    <div class="confirmation-message">${message}</div>
                    ${preview ? `
                        <div class="confirmation-preview">
                            <h4>Items to be affected:</h4>
                            ${preview}
                        </div>
                    ` : ''}
                </div>
                <div class="confirmation-actions">
                    <button class="confirmation-button cancel">${cancelText}</button>
                    <button class="confirmation-button confirm">${confirmText}</button>
                </div>
            </div>
        </div>
    `;

    // Add dialog to page
    document.body.insertAdjacentHTML('beforeend', dialogHTML);
    const overlay = document.querySelector('.confirmation-overlay');
    const cancelButton = overlay.querySelector('.confirmation-button.cancel');
    const confirmButton = overlay.querySelector('.confirmation-button.confirm');

    // Show dialog with animation
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);

    // Handle cancel
    const handleCancel = () => {
        overlay.classList.remove('show');
        setTimeout(() => {
            overlay.remove();
        }, 300);
        onCancel();
    };

    // Handle confirm
    const handleConfirm = () => {
        confirmButton.classList.add('loading');
        confirmButton.textContent = 'Processing...';

        // Execute confirmation action
        const result = onConfirm();

        // If onConfirm returns a promise, wait for it
        if (result && typeof result.then === 'function') {
            result.then(() => {
                overlay.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }).catch(() => {
                confirmButton.classList.remove('loading');
                confirmButton.textContent = confirmText;
            });
        } else {
            overlay.classList.remove('show');
            setTimeout(() => {
                overlay.remove();
            }, 300);
        }
    };

    // Event listeners
    cancelButton.addEventListener('click', handleCancel);
    confirmButton.addEventListener('click', handleConfirm);

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            handleCancel();
        }
    });

    // Close on Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            handleCancel();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    return overlay;
}

function confirmClearTargets() {
    const targetItems = targetList.querySelectorAll('li');
    const targetCount = targetItems.length;

    if (targetCount === 0) {
        // Show a friendly message instead of confirmation
        const message = document.createElement('div');
        message.className = 'temp-message info show';
        message.textContent = 'No targets to clear';
        message.style.position = 'fixed';
        message.style.top = '20px';
        message.style.right = '20px';
        message.style.zIndex = '10001';
        document.body.appendChild(message);

        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }, 2000);
        return;
    }

    // Create preview of targets to be cleared
    let previewHTML = '';
    targetItems.forEach((item, index) => {
        if (item.targetData) {
            previewHTML += `
                <div class="confirmation-preview-item">
                    ${index + 1}. ${item.targetData.forceType} ${item.targetData.targetType}
                    (${item.targetData.quantity}) at ${item.targetData.location.substring(0, 30)}...
                </div>
            `;
        }
    });

    createConfirmationDialog({
        title: 'Clear All Targets',
        message: `You are about to remove ${targetCount} target${targetCount !== 1 ? 's' : ''} from the list and map. This action cannot be undone.`,
        confirmText: 'Clear All',
        cancelText: 'Keep Targets',
        preview: previewHTML,
        onConfirm: () => {
            clearAllTargets(true);
        }
    });
}

function confirmSendTargets() {
    const targetItems = targetList.querySelectorAll('li');
    const targetCount = targetItems.length;

    if (targetCount === 0) {
        return; // Let the existing validation handle this
    }

    // Create preview of targets to be sent
    let previewHTML = '';
    targetItems.forEach((item, index) => {
        if (item.targetData) {
            previewHTML += `
                <div class="confirmation-preview-item">
                    ${index + 1}. ${item.targetData.forceType} ${item.targetData.targetType}
                    (${item.targetData.quantity}) at ${item.targetData.location.substring(0, 30)}...
                </div>
            `;
        }
    });

    createConfirmationDialog({
        title: 'Send Targets to Mortar Team',
        message: `You are about to transmit ${targetCount} target${targetCount !== 1 ? 's' : ''} to the mortar team for fire missions. Confirm transmission?`,
        confirmText: 'Send Targets',
        cancelText: 'Cancel',
        preview: previewHTML,
        onConfirm: () => {
            return sendTargetData(); // Return the promise for proper handling
        }
    });
}

// Distance measurement tool removed for cleaner codebase



function setupMapTools() {
    // Add zoom level indicator
    updateZoomIndicator();
    map.on('zoomend', updateZoomIndicator);

    // Setup comprehensive keyboard shortcuts
    setupKeyboardShortcuts();
}

// --- Keyboard Shortcuts System ---
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Don't trigger shortcuts when typing in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return;
        }

        // Zoom shortcuts
        if (e.key >= '1' && e.key <= '3' && e.ctrlKey) {
            e.preventDefault();
            const zoomLevels = [10, 13, 16];
            setZoomLevel(zoomLevels[parseInt(e.key) - 1]);
        }

        // Target management shortcuts
        else if (e.key === 'a' && e.ctrlKey) {
            e.preventDefault();
            focusAddTargetForm();
        } else if (e.key === 's' && e.ctrlKey) {
            e.preventDefault();
            if (targetList && targetList.children.length > 0) {
                confirmSendTargets();
            }
        } else if (e.key === 'Delete' && e.ctrlKey) {
            e.preventDefault();
            confirmClearTargets();
        }

        // Quick actions
        else if (e.key === 'g' && e.ctrlKey) {
            e.preventDefault();
            toggleGridLines();
        } else if (e.key === 'l' && e.ctrlKey) {
            e.preventDefault();
            toggleMapLegend();
        }

        // Batch operations shortcuts
        else if (e.key === 'b' && e.ctrlKey) {
            e.preventDefault();
            toggleBatchSelectionMode();
        } else if (e.key === 'a' && e.ctrlKey && e.shiftKey) {
            e.preventDefault();
            selectAllTargets();
        } else if (e.key === 'd' && e.ctrlKey && e.shiftKey) {
            e.preventDefault();
            deselectAllTargets();
        }

        // Help shortcut
        else if (e.key === 'F1' || (e.key === '?' && e.shiftKey)) {
            e.preventDefault();
            showKeyboardShortcutsHelp();
        }
    });
}

// --- Batch Operations Functions ---
function toggleBatchSelectionMode() {
    batchSelectionMode = !batchSelectionMode;

    if (batchSelectionMode) {
        // Show batch operations UI
        showBatchOperationsUI();
        // Add visual indicator to targets
        updateTargetListForBatchMode();
    } else {
        // Hide batch operations UI
        hideBatchOperationsUI();
        // Clear selections
        deselectAllTargets();
        // Remove visual indicators
        updateTargetListForBatchMode();
    }
}

function showBatchOperationsUI() {
    // Create batch operations panel if it doesn't exist
    let batchPanel = document.getElementById('batch-operations-panel');
    if (!batchPanel) {
        batchPanel = document.createElement('div');
        batchPanel.id = 'batch-operations-panel';
        batchPanel.className = 'batch-operations-panel';
        batchPanel.innerHTML = `
            <div class="batch-panel-header">
                <h4>📋 Batch Operations</h4>
                <button class="batch-close-btn" onclick="toggleBatchSelectionMode()">✕</button>
            </div>
            <div class="batch-panel-content">
                <div class="batch-stats">
                    <span id="batch-selected-count">0</span> targets selected
                </div>
                <div class="batch-actions">
                    <button onclick="selectAllTargets()" class="batch-action-btn">Select All</button>
                    <button onclick="deselectAllTargets()" class="batch-action-btn">Deselect All</button>
                    <button onclick="batchDeleteSelected()" class="batch-action-btn danger">Delete Selected</button>
                    <button onclick="batchSendSelected()" class="batch-action-btn primary">Send Selected</button>
                </div>
            </div>
        `;
        document.body.appendChild(batchPanel);
    }
    batchPanel.classList.add('show');
}

function hideBatchOperationsUI() {
    const batchPanel = document.getElementById('batch-operations-panel');
    if (batchPanel) {
        batchPanel.classList.remove('show');
    }
}

function updateTargetListForBatchMode() {
    const targetItems = targetList.querySelectorAll('li');
    targetItems.forEach(item => {
        if (batchSelectionMode) {
            // Add checkbox for batch selection
            if (!item.querySelector('.batch-checkbox')) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'batch-checkbox';
                checkbox.addEventListener('change', (e) => {
                    handleTargetSelection(item, e.target.checked);
                });
                item.insertBefore(checkbox, item.firstChild);
            }
            item.classList.add('batch-mode');
        } else {
            // Remove checkbox
            const checkbox = item.querySelector('.batch-checkbox');
            if (checkbox) {
                checkbox.remove();
            }
            item.classList.remove('batch-mode', 'batch-selected');
        }
    });
}

function handleTargetSelection(targetItem, isSelected) {
    if (isSelected) {
        selectedTargets.add(targetItem);
        targetItem.classList.add('batch-selected');
    } else {
        selectedTargets.delete(targetItem);
        targetItem.classList.remove('batch-selected');
    }
    updateBatchStats();
}

function selectAllTargets() {
    const targetItems = targetList.querySelectorAll('li');
    targetItems.forEach(item => {
        const checkbox = item.querySelector('.batch-checkbox');
        if (checkbox) {
            checkbox.checked = true;
            handleTargetSelection(item, true);
        }
    });
}

function deselectAllTargets() {
    selectedTargets.clear();
    const targetItems = targetList.querySelectorAll('li');
    targetItems.forEach(item => {
        const checkbox = item.querySelector('.batch-checkbox');
        if (checkbox) {
            checkbox.checked = false;
        }
        item.classList.remove('batch-selected');
    });
    updateBatchStats();
}

function updateBatchStats() {
    const countElement = document.getElementById('batch-selected-count');
    if (countElement) {
        countElement.textContent = selectedTargets.size;
    }
}

function batchDeleteSelected() {
    if (selectedTargets.size === 0) return;

    const targetData = Array.from(selectedTargets).map(item => item.targetData).filter(Boolean);
    let previewHTML = '';
    targetData.forEach((data, index) => {
        previewHTML += `
            <div class="confirmation-preview-item">
                ${index + 1}. ${data.forceType} ${data.targetType}
                (${data.quantity}) at ${data.location.substring(0, 30)}...
            </div>
        `;
    });

    createConfirmationDialog({
        title: 'Delete Selected Targets',
        message: `You are about to delete ${selectedTargets.size} selected target${selectedTargets.size !== 1 ? 's' : ''}. This action cannot be undone.`,
        confirmText: 'Delete Selected',
        cancelText: 'Cancel',
        preview: previewHTML,
        onConfirm: () => {
            // Remove selected targets
            selectedTargets.forEach(item => {
                if (item.marker) {
                    map.removeLayer(item.marker);
                    const markerIndex = targetMarkers.indexOf(item.marker);
                    if (markerIndex > -1) {
                        targetMarkers.splice(markerIndex, 1);
                    }
                }
                item.remove();
            });

            selectedTargets.clear();
            updateTargetCount();
            updateBatchStats();
            saveTargetsToLocalStorage();
        }
    });
}

function batchSendSelected() {
    if (selectedTargets.size === 0) return;

    // Create a temporary list with only selected targets for sending
    const selectedData = Array.from(selectedTargets).map(item => item.targetData).filter(Boolean);

    // Show confirmation with preview
    let previewHTML = '';
    selectedData.forEach((data, index) => {
        previewHTML += `
            <div class="confirmation-preview-item">
                ${index + 1}. ${data.forceType} ${data.targetType}
                (${data.quantity}) at ${data.location.substring(0, 30)}...
            </div>
        `;
    });

    createConfirmationDialog({
        title: 'Send Selected Targets',
        message: `You are about to transmit ${selectedTargets.size} selected target${selectedTargets.size !== 1 ? 's' : ''} to the mortar team.`,
        confirmText: 'Send Selected',
        cancelText: 'Cancel',
        preview: previewHTML,
        onConfirm: () => {
            // Send only selected targets
            return sendSelectedTargets(selectedData);
        }
    });
}

async function sendSelectedTargets(targetData) {
    // Similar to sendTargetData but with custom data
    const sendButton = document.querySelector('.batch-action-btn.primary');
    const originalText = sendButton.textContent;

    updateConnectionStatus('connecting');
    sendButton.disabled = true;
    sendButton.innerHTML = '<span class="loading-spinner">[TRANSMITTING]</span> Sending...';

    // Demo mode - simulate backend without server
    if (DEMO_MODE) {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        try {
            // Store targets in localStorage for Mortar screen to access
            localStorage.setItem('fscsObserverTargets', JSON.stringify(targetData));
            console.log('Demo: Selected targets stored in localStorage for Mortar screen access');

            updateConnectionStatus('connected');
            sendButton.innerHTML = '[TRANSMITTED] Success (Demo)';
            console.log('Demo: Selected targets sent successfully.');
            return Promise.resolve({ message: 'Demo: Selected targets received successfully', count: targetData.length });
        } catch (error) {
            updateConnectionStatus('disconnected');
            sendButton.innerHTML = '[FAILED] Demo Error';
            console.error('Demo error sending selected targets:', error);
            return Promise.reject(error);
        } finally {
            setTimeout(() => {
                sendButton.disabled = false;
                sendButton.innerHTML = originalText;
            }, 2000);
        }
    }

    try {
        const response = await fetch('http://localhost:3000/api/sendTargets', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(targetData)
        });

        if (response.ok) {
            updateConnectionStatus('connected');
            sendButton.innerHTML = '[TRANSMITTED] Success';
            console.log('Selected targets sent successfully.');
        } else {
            updateConnectionStatus('disconnected');
            sendButton.innerHTML = '[FAILED] Send Error';
            console.error(`Error sending selected targets: ${response.status}`);
        }
    } catch (error) {
        updateConnectionStatus('disconnected');
        sendButton.innerHTML = '[FAILED] Connection Error';
        console.error('Network error sending selected targets:', error);
    } finally {
        setTimeout(() => {
            sendButton.disabled = false;
            sendButton.innerHTML = originalText;
        }, 3000);
    }
}

function focusAddTargetForm() {
    const quantityInput = document.getElementById('target-quantity');
    if (quantityInput) {
        quantityInput.focus();
        quantityInput.select();
    }
}

function toggleGridLines() {
    const button = document.getElementById('clear-grid-lines-button');
    if (button) {
        // If grid lines exist, clear them, otherwise this would need grid creation logic
        if (currentGridLines.length > 0) {
            clearGrid();
        }
    }
}

function toggleMapLegend() {
    const legend = document.querySelector('.map-legend');
    if (legend) {
        if (legend.classList.contains('expanded')) {
            legend.classList.remove('expanded');
            legend.classList.add('collapsed');
        } else {
            legend.classList.remove('collapsed');
            legend.classList.add('expanded');
        }
    }
}

function showKeyboardShortcutsHelp() {
    const helpContent = `
        <div class="shortcuts-help">
            <h3>🎯 Vector-Fire Keyboard Shortcuts</h3>
            <div class="shortcuts-grid">
                <div class="shortcut-category">
                    <h4>Target Management</h4>
                    <div class="shortcut-item"><kbd>Ctrl + A</kbd> Focus Add Target Form</div>
                    <div class="shortcut-item"><kbd>Ctrl + S</kbd> Send Targets</div>
                    <div class="shortcut-item"><kbd>Ctrl + Del</kbd> Clear All Targets</div>
                </div>
                <div class="shortcut-category">
                    <h4>Map Controls</h4>
                    <div class="shortcut-item"><kbd>Ctrl + M</kbd> Toggle Measurement Tool</div>
                    <div class="shortcut-item"><kbd>Ctrl + C</kbd> Clear Measurement</div>
                    <div class="shortcut-item"><kbd>Ctrl + G</kbd> Toggle Grid Lines</div>
                    <div class="shortcut-item"><kbd>Ctrl + L</kbd> Toggle Legend</div>
                </div>
                <div class="shortcut-category">
                    <h4>Zoom Controls</h4>
                    <div class="shortcut-item"><kbd>Ctrl + 1</kbd> Strategic View</div>
                    <div class="shortcut-item"><kbd>Ctrl + 2</kbd> Operational View</div>
                    <div class="shortcut-item"><kbd>Ctrl + 3</kbd> Tactical View</div>
                </div>
                <div class="shortcut-category">
                    <h4>General</h4>
                    <div class="shortcut-item"><kbd>F1</kbd> or <kbd>Shift + ?</kbd> Show This Help</div>
                    <div class="shortcut-item"><kbd>Esc</kbd> Cancel Current Action</div>
                </div>
            </div>
        </div>
    `;

    createConfirmationDialog({
        title: 'Keyboard Shortcuts',
        message: helpContent,
        confirmText: 'Got it!',
        cancelText: '',
        onConfirm: () => {},
        onCancel: () => {}
    });
}

function setZoomLevel(level) {
    map.setZoom(level);
    updateZoomIndicator();
}

function updateZoomIndicator() {
    const zoomElement = document.getElementById('current-zoom');
    if (zoomElement) {
        zoomElement.textContent = map.getZoom();
    }
}

// --- Accessibility Functions ---
function toggleAccessibilityControls() {
    let panel = document.getElementById('accessibility-controls');
    if (!panel) {
        panel = createAccessibilityControlsPanel();
    }

    panel.classList.toggle('show');
}

function createAccessibilityControlsPanel() {
    const panel = document.createElement('div');
    panel.id = 'accessibility-controls';
    panel.className = 'accessibility-controls';
    panel.innerHTML = `
        <h4>♿ Accessibility Options</h4>
        <div class="accessibility-option">
            <label for="high-contrast-toggle">High Contrast Mode</label>
            <div class="accessibility-toggle" id="high-contrast-toggle" onclick="toggleHighContrast()"></div>
        </div>
        <div class="accessibility-option">
            <label for="night-vision-toggle">Night Vision Mode</label>
            <div class="accessibility-toggle" id="night-vision-toggle" onclick="toggleNightVision()"></div>
        </div>
        <div class="accessibility-option">
            <label for="large-touch-toggle">Large Touch Targets</label>
            <div class="accessibility-toggle" id="large-touch-toggle" onclick="toggleLargeTouchMode()"></div>
        </div>
        <div class="accessibility-option">
            <label for="reduced-motion-toggle">Reduce Motion</label>
            <div class="accessibility-toggle" id="reduced-motion-toggle" onclick="toggleReducedMotion()"></div>
        </div>
        <button onclick="toggleAccessibilityControls()" style="width: 100%; margin-top: 1rem; padding: 0.75rem; background: var(--primary-color); color: white; border: none; border-radius: 0.5rem; cursor: pointer;">Close</button>
    `;

    document.body.appendChild(panel);
    return panel;
}

function toggleHighContrast() {
    document.body.classList.toggle('high-contrast-mode');
    const toggle = document.getElementById('high-contrast-toggle');
    toggle.classList.toggle('active');

    // Save preference
    localStorage.setItem('high-contrast-mode', document.body.classList.contains('high-contrast-mode'));
}

function toggleNightVision() {
    document.body.classList.toggle('night-vision-mode');
    const toggle = document.getElementById('night-vision-toggle');
    toggle.classList.toggle('active');

    // Save preference
    localStorage.setItem('night-vision-mode', document.body.classList.contains('night-vision-mode'));
}

function toggleLargeTouchMode() {
    document.body.classList.toggle('large-touch-mode');
    const toggle = document.getElementById('large-touch-toggle');
    toggle.classList.toggle('active');

    // Save preference
    localStorage.setItem('large-touch-mode', document.body.classList.contains('large-touch-mode'));
}

function toggleReducedMotion() {
    document.body.classList.toggle('reduced-motion');
    const toggle = document.getElementById('reduced-motion-toggle');
    toggle.classList.toggle('active');

    // Save preference
    localStorage.setItem('reduced-motion', document.body.classList.contains('reduced-motion'));

    // Apply reduced motion CSS
    if (document.body.classList.contains('reduced-motion')) {
        const style = document.createElement('style');
        style.id = 'reduced-motion-style';
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    } else {
        const style = document.getElementById('reduced-motion-style');
        if (style) style.remove();
    }
}

function loadAccessibilityPreferences() {
    // Load saved accessibility preferences
    if (localStorage.getItem('high-contrast-mode') === 'true') {
        document.body.classList.add('high-contrast-mode');
    }

    if (localStorage.getItem('night-vision-mode') === 'true') {
        document.body.classList.add('night-vision-mode');
    }

    if (localStorage.getItem('large-touch-mode') === 'true') {
        document.body.classList.add('large-touch-mode');
    }

    if (localStorage.getItem('reduced-motion') === 'true') {
        toggleReducedMotion();
    }
}

function addAccessibilityFeatures() {
    // Add skip navigation link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'sr-only';
    skipLink.style.position = 'absolute';
    skipLink.style.top = '10px';
    skipLink.style.left = '10px';
    skipLink.style.zIndex = '10000';
    skipLink.style.background = 'var(--primary-color)';
    skipLink.style.color = 'white';
    skipLink.style.padding = '0.5rem 1rem';
    skipLink.style.borderRadius = '0.25rem';
    skipLink.style.textDecoration = 'none';

    skipLink.addEventListener('focus', () => {
        skipLink.classList.remove('sr-only');
    });

    skipLink.addEventListener('blur', () => {
        skipLink.classList.add('sr-only');
    });

    document.body.insertBefore(skipLink, document.body.firstChild);

    // Add main content landmark
    const mainContent = document.querySelector('.container');
    if (mainContent) {
        mainContent.id = 'main-content';
        mainContent.setAttribute('role', 'main');
    }

    // Add ARIA labels to important elements
    const statusBar = document.querySelector('.status-bar');
    if (statusBar) {
        statusBar.setAttribute('role', 'status');
        statusBar.setAttribute('aria-label', 'System status information');
    }

    const targetList = document.getElementById('target-list-items');
    if (targetList) {
        targetList.setAttribute('role', 'list');
        targetList.setAttribute('aria-label', 'Target list');
    }

    // Add keyboard navigation for quick action toolbar
    const quickActions = document.querySelectorAll('.quick-action-button');
    quickActions.forEach((button, index) => {
        button.setAttribute('tabindex', '0');
        button.setAttribute('role', 'button');

        button.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            } else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                e.preventDefault();
                const nextButton = quickActions[index + 1] || quickActions[0];
                nextButton.focus();
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                e.preventDefault();
                const prevButton = quickActions[index - 1] || quickActions[quickActions.length - 1];
                prevButton.focus();
            }
        });
    });
}

// Enhanced keyboard navigation for forms
function setupFormKeyboardNavigation() {
    const formElements = document.querySelectorAll('input, select, textarea, button');

    formElements.forEach((element, index) => {
        element.addEventListener('keydown', (e) => {
            // Enhanced Tab navigation
            if (e.key === 'Tab' && !e.shiftKey) {
                // Tab forward - let default behavior handle this
            } else if (e.key === 'Tab' && e.shiftKey) {
                // Shift+Tab backward - let default behavior handle this
            }

            // Arrow key navigation for form fields
            else if (e.key === 'ArrowDown' && e.ctrlKey) {
                e.preventDefault();
                const nextElement = formElements[index + 1] || formElements[0];
                nextElement.focus();
            } else if (e.key === 'ArrowUp' && e.ctrlKey) {
                e.preventDefault();
                const prevElement = formElements[index - 1] || formElements[formElements.length - 1];
                prevElement.focus();
            }

            // Escape to clear focus
            else if (e.key === 'Escape') {
                element.blur();
            }
        });
    });
}

// Enhanced error announcement for screen readers
function announceToScreenReader(message, priority = 'polite') {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

// Enhanced focus management for modals and dialogs
function trapFocus(element) {
    const focusableElements = element.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    element.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        }

        if (e.key === 'Escape') {
            element.style.display = 'none';
            // Return focus to trigger element if available
            const trigger = document.querySelector('[data-modal-trigger]');
            if (trigger) trigger.focus();
        }
    });

    // Focus first element when modal opens
    if (firstFocusable) firstFocusable.focus();
}

// --- Initialize Status Bar on Page Load ---
document.addEventListener('DOMContentLoaded', function() {
    // Initialize status bar after a short delay to ensure all elements are loaded
    setTimeout(() => {
        initializeStatusBar();
        initializeTheme(); // Initialize retro mode theme
        initializeCollapsibles(); // Initialize collapsible sections

        // Load targets from localStorage which will also update the target count
        loadTargetsFromLocalStorage();

        // Setup form validation
        setupFormValidation();

        // Setup tooltips
        setupTooltips();

        // Setup map tools
        setupMapTools();

        // Load accessibility preferences
        loadAccessibilityPreferences();

        // Add accessibility features
        addAccessibilityFeatures();

        // Setup enhanced keyboard navigation
        setupFormKeyboardNavigation();

        // Setup enhanced visual feedback
        setupEnhancedFormFeedback();

        // Add ripple effect to all buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                addButtonRippleEffect(button);
            });
        });

        // Setup mobile optimizations
        setupMobileOptimizations();
    }, 100);
});

// --- Enhanced Visual Feedback Functions ---

function addButtonRippleEffect(button) {
    if (!button) return;

    // Add ripple class if not already present
    if (!button.classList.contains('button-ripple')) {
        button.classList.add('button-ripple');
    }

    // Trigger ripple animation
    button.classList.remove('button-ripple');
    void button.offsetWidth; // Force reflow
    button.classList.add('button-ripple');
}

function showButtonSuccess(button) {
    if (!button) return;

    const originalText = button.textContent;
    const originalClass = button.className;

    // Add success state
    button.classList.add('success');
    button.textContent = '✓ Added';

    // Reset after animation
    setTimeout(() => {
        button.className = originalClass;
        button.textContent = originalText;
    }, 1200);
}

function showButtonError(button, message = 'Error') {
    if (!button) return;

    const originalText = button.textContent;
    const originalClass = button.className;

    // Add error state
    button.classList.add('error');
    button.textContent = message;

    // Reset after animation
    setTimeout(() => {
        button.className = originalClass;
        button.textContent = originalText;
    }, 2000);
}

function showButtonLoading(button, loadingText = 'Loading...') {
    if (!button) return;

    const originalText = button.textContent;
    button.classList.add('loading');
    button.textContent = loadingText;
    button.disabled = true;

    return {
        stop: () => {
            button.classList.remove('loading');
            button.textContent = originalText;
            button.disabled = false;
        }
    };
}

// Enhanced form field focus feedback
function setupEnhancedFormFeedback() {
    const formFields = document.querySelectorAll('input, select, textarea');

    formFields.forEach(field => {
        field.addEventListener('focus', () => {
            field.parentElement?.classList.add('field-focused');
        });

        field.addEventListener('blur', () => {
            field.parentElement?.classList.remove('field-focused');
        });

        // Add validation feedback
        field.addEventListener('input', () => {
            if (field.checkValidity()) {
                field.classList.remove('field-invalid');
                field.classList.add('field-valid');
            } else {
                field.classList.remove('field-valid');
                field.classList.add('field-invalid');
            }
        });
    });
}

// --- Mobile Responsiveness Enhancements ---

function setupMobileOptimizations() {
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isMobile || isTouchDevice) {
        document.body.classList.add('mobile-device');

        // Enhanced touch feedback
        setupTouchFeedback();

        // Mobile-specific map optimizations
        setupMobileMapOptimizations();

        // Prevent zoom on double-tap for form inputs
        preventDoubleTapZoom();
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            // Resize map after orientation change
            if (window.map) {
                map.invalidateSize();
            }
            if (window.mortarMap) {
                mortarMap.invalidateSize();
            }
        }, 100);
    });

    // Handle viewport changes for mobile browsers
    window.addEventListener('resize', debounce(() => {
        if (window.map) {
            map.invalidateSize();
        }
        if (window.mortarMap) {
            mortarMap.invalidateSize();
        }
    }, 250));
}

function setupTouchFeedback() {
    // Add touch feedback to buttons
    const buttons = document.querySelectorAll('button');

    buttons.forEach(button => {
        button.addEventListener('touchstart', () => {
            button.classList.add('touch-active');
        });

        button.addEventListener('touchend', () => {
            setTimeout(() => {
                button.classList.remove('touch-active');
            }, 150);
        });

        button.addEventListener('touchcancel', () => {
            button.classList.remove('touch-active');
        });
    });
}

function setupMobileMapOptimizations() {
    if (window.map) {
        // Disable map zoom on double tap to prevent conflicts
        map.doubleClickZoom.disable();

        // Add custom double-tap zoom with delay
        let lastTap = 0;
        map.on('click', (e) => {
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;

            if (tapLength < 500 && tapLength > 0) {
                // Double tap detected
                map.setZoom(map.getZoom() + 1);
                e.originalEvent.preventDefault();
            }
            lastTap = currentTime;
        });

        // Optimize map controls for mobile
        const zoomControl = document.querySelector('.leaflet-control-zoom');
        if (zoomControl) {
            zoomControl.style.marginTop = '60px'; // Account for mobile browser UI
        }
    }
}

function preventDoubleTapZoom() {
    // Prevent double-tap zoom on form inputs
    const inputs = document.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        input.addEventListener('touchend', (e) => {
            e.preventDefault();
            input.focus();
        });
    });
}

// Debounce utility for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// --- Demo Mode Functions (for GitHub deployment) ---

async function sendTargetDataDemo(targetListData) {
    const sendButton = document.getElementById('send-targets-button');

    // Show loading state with enhanced feedback
    const loadingState = showButtonLoading(sendButton, 'Transmitting...');
    sendButton.classList.add('sending');

    // Update connection status to show sending
    updateConnectionStatus('connecting');

    // Show feedback message
    if (sendFeedbackEl) {
        sendFeedbackEl.textContent = "Transmitting target data...";
        sendFeedbackEl.className = 'feedback-message sending';
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    try {
        // Store targets in demo storage
        demoTargets = [...targetListData];

        // Also store in localStorage for Mortar screen to access
        localStorage.setItem('fscsObserverTargets', JSON.stringify(targetListData));
        console.log('Demo: Targets stored in localStorage for Mortar screen access');

        // Update connection status to connected
        updateConnectionStatus('connected');

        // Show success feedback
        if (sendFeedbackEl) {
            sendFeedbackEl.textContent = "[SUCCESS] Targets transmitted successfully (Demo Mode)";
            sendFeedbackEl.classList.remove('sending');
            sendFeedbackEl.classList.add('success');
        }

        // Show success state on button with enhanced feedback
        loadingState.stop();
        showButtonSuccess(sendButton);

        console.log('Demo: Target data sent successfully.', demoTargets);

        // Announce to screen readers
        announceToScreenReader(`Demo: ${targetListData.length} targets transmitted successfully`);

        return Promise.resolve({ message: 'Demo: Targets received successfully', count: demoTargets.length });

    } catch (error) {
        // Handle demo errors
        updateConnectionStatus('disconnected');

        if (sendFeedbackEl) {
            sendFeedbackEl.textContent = "[ERROR] Demo transmission error";
            sendFeedbackEl.classList.remove('sending');
            sendFeedbackEl.classList.add('error');
        }

        loadingState.stop();
        showButtonError(sendButton, 'Demo Error');

        console.error('Demo error:', error);
        return Promise.reject(error);
    } finally {
        // Reset feedback after delay
        setTimeout(() => {
            if (sendFeedbackEl) {
                sendFeedbackEl.textContent = "";
                sendFeedbackEl.className = 'feedback-message';
            }
        }, 3000);
    }
}

// Demo function to get targets (for mortar screen)
function getTargetsDemo() {
    return new Promise((resolve) => {
        // Simulate network delay
        setTimeout(() => {
            resolve(demoTargets);
        }, 500);
    });
}

// --- Visual Polish & Animation Enhancements ---

function setupVisualPolish() {
    // Add staggered animations to existing target list items
    animateExistingTargets();

    // Setup smooth scrolling for better UX
    setupSmoothScrolling();

    // Add entrance animations for form elements
    setupFormAnimations();

    // Setup enhanced loading states
    setupEnhancedLoadingStates();
}

function animateExistingTargets() {
    const targetItems = document.querySelectorAll('#target-list-items li');
    targetItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('animate-in');
    });
}

function setupSmoothScrolling() {
    // Smooth scroll to target when "Go To" button is clicked
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('go-to-button')) {
            const targetElement = document.getElementById('map');
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }
    });
}

function setupFormAnimations() {
    const formGroups = document.querySelectorAll('.form-group');

    // Add entrance animations with stagger
    formGroups.forEach((group, index) => {
        group.style.animationDelay = `${index * 0.1}s`;
        group.classList.add('fade-in-up');
    });

    // Enhanced focus animations
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            input.parentElement?.classList.add('field-focused');

            // Add subtle scale animation
            input.style.transform = 'scale(1.02)';
            setTimeout(() => {
                input.style.transform = 'scale(1)';
            }, 200);
        });

        input.addEventListener('blur', () => {
            input.parentElement?.classList.remove('field-focused');
        });
    });
}

function setupEnhancedLoadingStates() {
    // Override the existing loading function to add visual polish
    const originalShowButtonLoading = window.showButtonLoading;

    window.showButtonLoading = function(button, loadingText = 'Loading...') {
        if (!button) return;

        const originalText = button.textContent;
        const originalClass = button.className;

        // Add enhanced loading animation
        button.classList.add('loading', 'loading-enhanced');
        button.textContent = loadingText;
        button.disabled = true;

        // Add pulsing effect
        button.style.animation = 'pulse 1.5s ease-in-out infinite';

        return {
            stop: () => {
                button.classList.remove('loading', 'loading-enhanced');
                button.textContent = originalText;
                button.disabled = false;
                button.style.animation = '';
            }
        };
    };
}

// Enhanced target addition with smooth animations
function addTargetWithAnimation(targetData) {
    // Call the original function
    addTargetToListAndMap(targetData);

    // Add entrance animation to the new target
    setTimeout(() => {
        const newTarget = document.querySelector('#target-list-items li:last-child');
        if (newTarget) {
            newTarget.style.opacity = '0';
            newTarget.style.transform = 'translateX(-30px)';

            // Trigger animation
            requestAnimationFrame(() => {
                newTarget.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                newTarget.style.opacity = '1';
                newTarget.style.transform = 'translateX(0)';
            });
        }
    }, 50);
}

// Enhanced collapsible animations
function enhanceCollapsibles() {
    const collapsibles = document.querySelectorAll('.collapsible');

    collapsibles.forEach(collapsible => {
        const content = collapsible.nextElementSibling;

        collapsible.addEventListener('click', () => {
            const isActive = collapsible.classList.contains('active');

            if (isActive) {
                // Closing animation
                content.style.maxHeight = content.scrollHeight + 'px';
                requestAnimationFrame(() => {
                    content.style.maxHeight = '0';
                });
            } else {
                // Opening animation
                content.style.maxHeight = content.scrollHeight + 'px';

                // Reset max-height after animation
                setTimeout(() => {
                    if (collapsible.classList.contains('active')) {
                        content.style.maxHeight = 'none';
                    }
                }, 400);
            }
        });
    });
}

// Initialize visual polish
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        setupVisualPolish();
        enhanceCollapsibles();
    }, 200);
});